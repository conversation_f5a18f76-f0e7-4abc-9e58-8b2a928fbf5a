import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:get/get.dart';

class ConnectionDiagnostics {
  static List<String> diagnosticResults = [];

  static Future<void> runFullDiagnostics() async {
    diagnosticResults.clear();

    // Show loading dialog
    Get.dialog(
      AlertDialog(
        title: Text('Running Diagnostics...'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Testing connectivity...'),
          ],
        ),
      ),
      barrierDismissible: false,
    );

    await _testBasicConnectivity();
    await _testLaravelBackend();
    await _testFirebaseServices();
    await _testGoogleServices();

    // Close loading dialog
    Get.back();

    await _showDiagnosticResults();
  }

  static Future<void> _testBasicConnectivity() async {
    diagnosticResults.add("=== BASIC CONNECTIVITY TESTS ===");

    // Test basic internet
    try {
      final response = await http.get(Uri.parse('https://www.google.com'),
          headers: {
            'User-Agent': 'VentaCuba-Diagnostic/1.0'
          }).timeout(Duration(seconds: 10));

      if (response.statusCode == 200) {
        diagnosticResults.add("✅ Internet connection: WORKING");
      } else {
        diagnosticResults.add(
            "❌ Internet connection: FAILED (Status: ${response.statusCode})");
      }
    } catch (e) {
      diagnosticResults.add("❌ Internet connection: FAILED - $e");
    }

    // Test DNS resolution
    try {
      final addresses = await InternetAddress.lookup('ventacuba.co');
      if (addresses.isNotEmpty) {
        diagnosticResults.add(
            "✅ DNS resolution (ventacuba.co): WORKING - IP: ${addresses.first.address}");
      } else {
        diagnosticResults.add(
            "❌ DNS resolution (ventacuba.co): FAILED - No addresses found");
      }
    } catch (e) {
      diagnosticResults.add("❌ DNS resolution (ventacuba.co): FAILED - $e");
    }

    // Test Firebase domains
    List<String> firebaseDomains = [
      'firebase.googleapis.com',
      'firebaseapp.com',
      'firestore.googleapis.com',
      'fcm.googleapis.com'
    ];

    for (String domain in firebaseDomains) {
      try {
        final addresses = await InternetAddress.lookup(domain);
        if (addresses.isNotEmpty) {
          diagnosticResults.add(
              "✅ DNS resolution ($domain): WORKING - IP: ${addresses.first.address}");
        } else {
          diagnosticResults
              .add("❌ DNS resolution ($domain): FAILED - No addresses found");
        }
      } catch (e) {
        diagnosticResults.add("❌ DNS resolution ($domain): FAILED - $e");
      }
    }
  }

  static Future<void> _testLaravelBackend() async {
    diagnosticResults.add("\n=== LARAVEL BACKEND TESTS ===");

    // Test main website
    try {
      final response = await http.get(Uri.parse('https://ventacuba.co/'),
          headers: {
            'User-Agent': 'VentaCuba-Diagnostic/1.0'
          }).timeout(Duration(seconds: 15));

      if (response.statusCode == 200) {
        diagnosticResults.add("✅ Website (ventacuba.co): WORKING");
      } else {
        diagnosticResults.add(
            "❌ Website (ventacuba.co): FAILED (Status: ${response.statusCode})");
      }
    } catch (e) {
      diagnosticResults.add("❌ Website (ventacuba.co): FAILED - $e");
    }

    // Test API endpoint
    try {
      print('🔍 Testing API endpoint: https://ventacuba.co/api/categories');
      final response = await http
          .get(Uri.parse('https://ventacuba.co/api/categories'), headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'VentaCuba-Diagnostic/1.0'
      }).timeout(Duration(seconds: 15));

      print('🔍 API Response Status: ${response.statusCode}');
      print('🔍 API Response Headers: ${response.headers}');
      print(
          '🔍 API Response Body (first 200 chars): ${response.body.length > 200 ? response.body.substring(0, 200) + "..." : response.body}');

      if (response.statusCode == 200) {
        diagnosticResults.add("✅ API endpoint (/api/categories): WORKING");
        try {
          final data = jsonDecode(response.body);
          diagnosticResults.add("✅ API response parsing: WORKING");
          print(
              '🔍 API Response parsed successfully. Type: ${data.runtimeType}');
        } catch (e) {
          diagnosticResults.add("⚠️ API response parsing: FAILED - $e");
          print('🔍 API Response parsing error: $e');
        }
      } else {
        diagnosticResults.add(
            "❌ API endpoint (/api/categories): FAILED (Status: ${response.statusCode})");
        diagnosticResults.add(
            "Response: ${response.body.length > 100 ? response.body.substring(0, 100) + "..." : response.body}");
        print('🔍 API endpoint failed with status: ${response.statusCode}');
        print('🔍 Full response body: ${response.body}');
      }
    } catch (e) {
      diagnosticResults.add("❌ API endpoint (/api/categories): FAILED - $e");
      print('🔍 API endpoint exception: $e');
    }
  }

  static Future<void> _testFirebaseServices() async {
    diagnosticResults.add("\n=== FIREBASE SERVICES TESTS ===");

    // Test Firebase Core
    try {
      await Firebase.initializeApp();
      diagnosticResults.add("✅ Firebase Core: INITIALIZED");
    } catch (e) {
      diagnosticResults.add("❌ Firebase Core: FAILED - $e");
      return; // If core fails, other services will fail too
    }

    // Test Firebase App Check
    try {
      await FirebaseAppCheck.instance.activate(
        androidProvider: AndroidProvider.playIntegrity,
        appleProvider: AppleProvider.debug,
      );
      diagnosticResults.add("✅ Firebase App Check: ACTIVATED");
    } catch (e) {
      diagnosticResults.add("❌ Firebase App Check: FAILED - $e");
    }

    // Test Firebase Auth
    try {
      FirebaseAuth.instance;
      diagnosticResults.add("✅ Firebase Auth: INITIALIZED");
    } catch (e) {
      diagnosticResults.add("❌ Firebase Auth: FAILED - $e");
    }

    // Test Firebase Messaging
    try {
      FirebaseMessaging messaging = FirebaseMessaging.instance;
      String? token = await messaging.getToken().timeout(Duration(seconds: 10));
      if (token != null) {
        diagnosticResults.add("✅ Firebase Messaging: WORKING - Token received");
      } else {
        diagnosticResults
            .add("❌ Firebase Messaging: FAILED - No token received");
      }
    } catch (e) {
      diagnosticResults.add("❌ Firebase Messaging: FAILED - $e");
    }

    // Test Firestore
    try {
      FirebaseFirestore firestore = FirebaseFirestore.instance;
      firestore.settings = const Settings(
        persistenceEnabled: false,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );
      diagnosticResults.add("✅ Firestore: INITIALIZED");
    } catch (e) {
      diagnosticResults.add("❌ Firestore: FAILED - $e");
    }

    // Test Firebase Storage
    try {
      FirebaseStorage.instance;
      diagnosticResults.add("✅ Firebase Storage: INITIALIZED");
    } catch (e) {
      diagnosticResults.add("❌ Firebase Storage: FAILED - $e");
    }
  }

  static Future<void> _testGoogleServices() async {
    diagnosticResults.add("\n=== GOOGLE SERVICES TESTS ===");

    // Test Google Maps API
    try {
      final response = await http.get(
          Uri.parse(
              'https://maps.googleapis.com/maps/api/geocode/json?address=Havana,Cuba&key=AIzaSyBx95Bvl9O-US2sQpqZ41GdsHIprnXvJv8'),
          headers: {
            'User-Agent': 'VentaCuba-Diagnostic/1.0'
          }).timeout(Duration(seconds: 15));

      if (response.statusCode == 200) {
        diagnosticResults.add("✅ Google Maps API: WORKING");
      } else {
        diagnosticResults
            .add("❌ Google Maps API: FAILED (Status: ${response.statusCode})");
      }
    } catch (e) {
      diagnosticResults.add("❌ Google Maps API: FAILED - $e");
    }

    // Test FCM endpoint
    try {
      final response = await http.get(Uri.parse('https://fcm.googleapis.com/'),
          headers: {
            'User-Agent': 'VentaCuba-Diagnostic/1.0'
          }).timeout(Duration(seconds: 10));

      if (response.statusCode == 200 || response.statusCode == 404) {
        diagnosticResults.add("✅ FCM endpoint: REACHABLE");
      } else {
        diagnosticResults
            .add("❌ FCM endpoint: FAILED (Status: ${response.statusCode})");
      }
    } catch (e) {
      diagnosticResults.add("❌ FCM endpoint: FAILED - $e");
    }

    // Test specific Firebase endpoints that might be blocked
    List<String> firebaseEndpoints = [
      'https://firebase.googleapis.com/v1beta1/projects/ventacuba-latest-version',
      'https://firestore.googleapis.com/v1/projects/ventacuba-latest-version/databases/(default)/documents',
      'https://ventacuba-latest-version.firebasestorage.app/',
    ];

    for (String endpoint in firebaseEndpoints) {
      try {
        final response = await http.get(Uri.parse(endpoint), headers: {
          'User-Agent': 'VentaCuba-Diagnostic/1.0'
        }).timeout(Duration(seconds: 10));

        if (response.statusCode == 200 ||
            response.statusCode == 401 ||
            response.statusCode == 403) {
          diagnosticResults.add(
              "✅ Firebase endpoint reachable: ${Uri.parse(endpoint).host}");
        } else {
          diagnosticResults.add(
              "❌ Firebase endpoint failed: ${Uri.parse(endpoint).host} (Status: ${response.statusCode})");
        }
      } catch (e) {
        diagnosticResults.add(
            "❌ Firebase endpoint failed: ${Uri.parse(endpoint).host} - $e");
      }
    }
  }

  static Future<void> _showDiagnosticResults() async {
    diagnosticResults.add("\n=== DIAGNOSTIC SUMMARY ===");
    diagnosticResults.add("Timestamp: ${DateTime.now().toIso8601String()}");
    diagnosticResults.add("Platform: ${Platform.operatingSystem}");
    diagnosticResults.add("User Agent: VentaCuba-Diagnostic/1.0");

    // Print all results to debug console
    String fullResults = diagnosticResults.join('\n');
    print('\n🔍 ===== CUBA CONNECTIVITY DIAGNOSTICS =====');
    print(fullResults);
    print('🔍 ============================================\n');

    // Show results in a dialog
    Get.dialog(
      AlertDialog(
        title: Text('Connection Diagnostics'),
        content: Container(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Text(
              diagnosticResults.join('\n'),
              style: TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Close'),
          ),
          TextButton(
            onPressed: () {
              _copyToClipboard();
            },
            child: Text('Copy Results'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  static void _copyToClipboard() {
    // You can implement clipboard functionality here
    Get.snackbar(
      'Copied',
      'Diagnostic results copied to clipboard',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  static String getResultsAsString() {
    return diagnosticResults.join('\n');
  }
}
