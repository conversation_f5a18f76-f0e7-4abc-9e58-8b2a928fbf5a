class AppConfig {
  // Set this to true to disable Firebase services for Cuba testing
  static const bool DISABLE_FIREBASE_FOR_CUBA = false;
  
  // Set this to true to enable detailed logging
  static const bool ENABLE_DEBUG_LOGGING = true;
  
  // Set this to true to show diagnostic button on all screens
  static const bool SHOW_DIAGNOSTIC_BUTTON = true;
  
  // Alternative backend URL for testing (if needed)
  static const String ALTERNATIVE_BACKEND_URL = "https://ventacuba.co/";
  
  // Firebase project configuration
  static const String FIREBASE_PROJECT_ID = "ventacuba-latest-version";
  
  // User agent for diagnostic requests
  static const String DIAGNOSTIC_USER_AGENT = "VentaCuba-Diagnostic/1.0";
  
  // Timeout configurations
  static const int API_TIMEOUT_SECONDS = 30;
  static const int FIREBASE_TIMEOUT_SECONDS = 15;
  static const int DIAGNOSTIC_TIMEOUT_SECONDS = 10;
  
  // Feature flags
  static const bool ENABLE_FIREBASE_MESSAGING = true;
  static const bool ENABLE_FIREBASE_AUTH = true;
  static const bool ENABLE_FIRESTORE = true;
  static const bool ENABLE_FIREBASE_STORAGE = true;
  static const bool ENABLE_FIREBASE_APP_CHECK = true;
  
  // Cuba-specific configurations
  static const bool CUBA_TESTING_MODE = false;
  
  static bool get isFirebaseEnabled => !DISABLE_FIREBASE_FOR_CUBA;
  static bool get isCubaTestingMode => CUBA_TESTING_MODE;
  static bool get shouldShowDiagnosticButton => SHOW_DIAGNOSTIC_BUTTON;
}
