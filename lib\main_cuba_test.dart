import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show SystemChrome, SystemUiOverlayStyle;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:venta_cuba/Controllers/auth_controller.dart';
import 'package:venta_cuba/Controllers/location_controller.dart';
import 'package:venta_cuba/Controllers/theme_controller.dart';
import 'package:venta_cuba/Services/Notfication/notficationservice.dart';
import 'package:venta_cuba/languages/languages.dart';
import 'package:venta_cuba/view/splash%20Screens/white_screen.dart';
import 'package:venta_cuba/view/constants/theme_config.dart';
import 'package:venta_cuba/Utils/app_config.dart';
import 'package:venta_cuba/Utils/global_variabel.dart';
import 'package:venta_cuba/Utils/connection_diagnostics.dart';
import 'Notification/firebase_messaging.dart';

// Background message handler - MUST be top-level function
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (AppConfig.isFirebaseEnabled) {
    await Firebase.initializeApp();
    print('🔥 Background message received: ${message.messageId}');
    print('🔥 Background message data: ${message.data}');
    print(
        '🔥 Background notification: ${message.notification?.title} - ${message.notification?.body}');
  }
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SharedPreferences.getInstance();

  // Only initialize Firebase if not disabled for Cuba testing
  if (AppConfig.isFirebaseEnabled) {
    try {
      await Firebase.initializeApp();
      print('🔥 Firebase initialized successfully');

      // Register background message handler
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);

      await Future.delayed(Duration(seconds: 1));

      // Firebase App Check
      if (AppConfig.ENABLE_FIREBASE_APP_CHECK) {
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.playIntegrity,
          appleProvider: AppleProvider.debug,
        );
        print('🔥 Firebase App Check activated');
      }
    } catch (e) {
      print('🔥 Firebase initialization failed: $e');
      print('🔥 Continuing without Firebase services...');
    }
  } else {
    print('🔥 Firebase disabled for Cuba testing mode');
  }

  runApp(const MyApp());
}

final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

String languageCode = "en";
String countryCode = "US";

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  FCM? firebaseMessaging;
  final locationCont = Get.put(LocationController());
  late final ThemeController themeController;
  NotificationService notificationService = NotificationService();

  @override
  void initState() {
    super.initState();

    // Initialize theme controller first
    themeController = Get.put(ThemeController());

    if (AppConfig.isFirebaseEnabled) {
      firebaseMessaging = FCM();
      notificationService.obtainCredentials();
      // Note: setNotifications will be called in didChangeDependencies when context is available
      firebaseMessaging!.streamCtrl.stream.listen((msgData) {
        debugPrint('messageData $msgData');
      });
    } else {
      print(
          '🔥 Skipping Firebase messaging initialization (Cuba testing mode)');
    }

    locationCheck();

    _fetchLocale().then((locale) {
      setState(() {
        _locale = locale;
      });
    });

    checkNotificationPermissions();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Initialize Firebase messaging with context now that it's available
    if (AppConfig.isFirebaseEnabled && firebaseMessaging != null) {
      firebaseMessaging!.setNotifications(context);
    }
  }

  Locale? _locale;

  Future<Locale> _fetchLocale() async {
    var prefs = await SharedPreferences.getInstance();
    String? languageCode = prefs.getString('languageCode');
    String? countryCode = prefs.getString('countryCode');

    if (languageCode != null && countryCode != null) {
      return Locale(languageCode, countryCode);
    } else {
      return const Locale('en', 'US');
    }
  }

  locationCheck() async {
    try {
      await locationCont.getLocation();
      print('Location check completed');
    } catch (e) {
      print('Error during location check: $e');
    }
  }

  checkNotificationPermissions() async {
    if (AppConfig.isFirebaseEnabled && firebaseMessaging != null) {
      try {
        await firebaseMessaging!.requestNotificationPermissions();
        print('Notification permissions checked');
      } catch (e) {
        print('Error checking notification permissions: $e');
      }
    }
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return Obx(() => GetMaterialApp(
              theme: ThemeConfig.lightTheme,
              darkTheme: ThemeConfig.darkTheme,
              themeMode: themeController.isDarkMode.value
                  ? ThemeMode.dark
                  : ThemeMode.light,
              translations: Languages(),
              locale: _locale ?? Locale(languageCode, countryCode),
              fallbackLocale: Locale(languageCode, countryCode),
              scaffoldMessengerKey: scaffoldMessengerKey,
              navigatorKey: navigatorKey,
              debugShowCheckedModeBanner: false,
              home: Stack(
                children: [
                  WhiteScreen(),
                  // Global diagnostic button
                  Positioned(
                    top: 50,
                    left: 20,
                    child: FloatingActionButton(
                      mini: true,
                      onPressed: () async {
                        await ConnectionDiagnostics.runFullDiagnostics();
                      },
                      backgroundColor: Colors.green,
                      child:
                          const Icon(Icons.network_check, color: Colors.white),
                    ),
                  ),
                ],
              ),
            ));
      },
    );
  }
}
